import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/theme/app_colors.dart';

/// Widget displaying detailed profile information
class ProfileInfoSection extends StatelessWidget {
  /// The app user to display
  final AppUser user;

  const ProfileInfoSection({super.key, required this.user});

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'Not set';

    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Profile Information',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 16.h),

        // Info container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information
              _InfoRow(
                icon: Symbols.person,
                label: 'Full Name',
                value: user.fullName ?? user.displayName ?? 'Not set',
              ),

              SizedBox(height: 12.h),

              _InfoRow(icon: Symbols.email, label: 'Email', value: user.email),

              if (user.phoneNumber != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.phone,
                  label: 'Phone',
                  value: user.phoneNumber!,
                ),
              ],

              // Academic Information
              if (user.grade != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.school,
                  label: 'Grade',
                  value: user.grade!,
                ),
              ],

              if (user.school != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.domain,
                  label: 'School',
                  value: user.school!,
                ),
              ],

              if (user.studentId != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.badge,
                  label: 'Student ID',
                  value: user.studentId!,
                ),
              ],

              // Classes section removed - enrollment is now tracked in ClassModel.studentIds

              // Subjects
              if (user.subjects.isNotEmpty) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.book,
                  label: 'Subjects',
                  value: user.subjects.join(', '),
                ),
              ],

              // Personal Information
              if (user.dateOfBirth != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.cake,
                  label: 'Date of Birth',
                  value: _formatDate(user.dateOfBirth),
                ),
              ],

              if (user.address != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.location_on,
                  label: 'Address',
                  value: user.address!,
                ),
              ],

              // Emergency Contact
              if (user.emergencyContact != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.emergency,
                  label: 'Emergency Contact',
                  value: user.emergencyContact!,
                ),
              ],

              if (user.emergencyContactPhone != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.emergency,
                  label: 'Emergency Phone',
                  value: user.emergencyContactPhone!,
                ),
              ],

              // Parent/Guardian Information (for students)
              if (user.parentGuardianName != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent/Guardian',
                  value: user.parentGuardianName!,
                ),
              ],

              if (user.parentGuardianPhone != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent Phone',
                  value: user.parentGuardianPhone!,
                ),
              ],

              if (user.parentGuardianEmail != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent Email',
                  value: user.parentGuardianEmail!,
                ),
              ],

              // Bio
              if (user.bio != null && user.bio!.isNotEmpty) ...[
                SizedBox(height: 16.h),
                Text(
                  'About',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isDark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  user.bio!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                    height: 1.4,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

/// Widget for displaying an information row
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: isDark
              ? AppColors.textSecondaryDark
              : AppColors.textSecondaryLight,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
              ),
              Flexible(
                child: Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
