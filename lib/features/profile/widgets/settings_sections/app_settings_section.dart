import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/auth/auth.dart';

/// Widget for managing app-specific settings
class AppSettingsSection extends ConsumerWidget {
  final AppUser user;

  const AppSettingsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final preferences = user.preferences;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'App Settings',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        Si<PERSON><PERSON><PERSON>(height: 12.h),
        
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              // Default Upload Quality
              _buildUploadQualityTile(context, ref, preferences.defaultUploadQuality),
              
              _buildDivider(theme),
              
              // Auto Download on WiFi
              _buildAppToggle(
                context,
                ref,
                icon: Symbols.wifi,
                title: 'Auto Download on WiFi',
                subtitle: 'Automatically download content on WiFi',
                value: preferences.autoDownloadOnWifi,
                onChanged: (value) => _updateAutoDownloadWifi(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Auto Download on Mobile
              _buildAppToggle(
                context,
                ref,
                icon: Symbols.signal_cellular_alt,
                title: 'Auto Download on Mobile',
                subtitle: 'Automatically download content on mobile data',
                value: preferences.autoDownloadOnMobile,
                onChanged: (value) => _updateAutoDownloadMobile(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Storage Management
              _buildActionTile(
                context,
                icon: Symbols.storage,
                title: 'Storage Management',
                subtitle: 'Manage downloaded files and cache',
                onTap: () => _showStorageDialog(context, ref),
              ),
              
              _buildDivider(theme),
              
              // Clear Cache
              _buildActionTile(
                context,
                icon: Symbols.delete_sweep,
                title: 'Clear Cache',
                subtitle: 'Free up space by clearing cached data',
                onTap: () => _showClearCacheDialog(context, ref),
              ),
              
              _buildDivider(theme),
              
              // App Version
              _buildInfoTile(
                context,
                icon: Symbols.info,
                title: 'App Version',
                subtitle: '1.0.0 (Build 1)', // TODO: Get from package info
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadQualityTile(BuildContext context, WidgetRef ref, String currentQuality) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        Symbols.high_quality,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        'Default Upload Quality',
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        _getQualityLabel(currentQuality),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Symbols.chevron_right,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: () => _showUploadQualityDialog(context, ref, currentQuality),
    );
  }

  Widget _buildAppToggle(
    BuildContext context,
    WidgetRef ref, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Symbols.chevron_right,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Divider(
      height: 1,
      color: theme.colorScheme.outline.withValues(alpha: 0.2),
    );
  }

  String _getQualityLabel(String quality) {
    switch (quality) {
      case 'low':
        return 'Low (Faster upload)';
      case 'medium':
        return 'Medium (Balanced)';
      case 'high':
        return 'High (Best quality)';
      default:
        return 'Medium (Balanced)';
    }
  }

  void _showUploadQualityDialog(BuildContext context, WidgetRef ref, String currentQuality) {
    final qualities = [
      {'value': 'low', 'label': 'Low (Faster upload)'},
      {'value': 'medium', 'label': 'Medium (Balanced)'},
      {'value': 'high', 'label': 'High (Best quality)'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upload Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: qualities.map((quality) {
            return RadioListTile<String>(
              value: quality['value']!,
              groupValue: currentQuality,
              title: Text(quality['label']!),
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  _updateUploadQuality(ref, value);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _updateUploadQuality(WidgetRef ref, String quality) {
    // TODO: Implement upload quality update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text('Upload quality set to ${_getQualityLabel(quality)}'),
      ),
    );
  }

  void _updateAutoDownloadWifi(WidgetRef ref, bool enabled) {
    // TODO: Implement auto download WiFi update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Auto download on WiFi enabled' 
              : 'Auto download on WiFi disabled',
        ),
      ),
    );
  }

  void _updateAutoDownloadMobile(WidgetRef ref, bool enabled) {
    // TODO: Implement auto download mobile update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Auto download on mobile enabled' 
              : 'Auto download on mobile disabled',
        ),
      ),
    );
  }

  void _showStorageDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Management'),
        content: const Text(
          'Storage management functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear all cached data?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement cache clearing
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully'),
                ),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
