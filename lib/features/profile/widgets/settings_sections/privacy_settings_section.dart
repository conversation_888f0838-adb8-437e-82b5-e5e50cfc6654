import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/auth/auth.dart';

/// Widget for managing privacy and security settings
class PrivacySettingsSection extends ConsumerWidget {
  final AppUser user;

  const PrivacySettingsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final preferences = user.preferences;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Privacy & Security',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        SizedBox(height: 12.h),
        
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              // Profile Visibility
              _buildPrivacyToggle(
                context,
                ref,
                icon: Symbols.visibility,
                title: 'Profile Visibility',
                subtitle: 'Allow others to see your profile',
                value: preferences.profileVisibilityEnabled,
                onChanged: (value) => _updateProfileVisibility(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Discoverable by Email
              _buildPrivacyToggle(
                context,
                ref,
                icon: Symbols.search,
                title: 'Discoverable by Email',
                subtitle: 'Allow others to find you by email address',
                value: preferences.discoverableByEmail,
                onChanged: (value) => _updateDiscoverableByEmail(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Change Password
              _buildActionTile(
                context,
                icon: Symbols.lock,
                title: 'Change Password',
                subtitle: 'Update your account password',
                onTap: () => _showChangePasswordDialog(context, ref),
              ),
              
              _buildDivider(theme),
              
              // Two-Factor Authentication
              _buildActionTile(
                context,
                icon: Symbols.security,
                title: 'Two-Factor Authentication',
                subtitle: 'Add an extra layer of security',
                onTap: () => _showTwoFactorDialog(context, ref),
              ),
              
              _buildDivider(theme),
              
              // Data & Privacy
              _buildActionTile(
                context,
                icon: Symbols.shield,
                title: 'Data & Privacy',
                subtitle: 'Manage your data and privacy settings',
                onTap: () => _showDataPrivacyDialog(context, ref),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacyToggle(
    BuildContext context,
    WidgetRef ref, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Symbols.chevron_right,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Divider(
      height: 1,
      color: theme.colorScheme.outline.withValues(alpha: 0.2),
    );
  }

  void _updateProfileVisibility(WidgetRef ref, bool enabled) {
    // TODO: Implement profile visibility update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Profile is now visible to others' 
              : 'Profile is now private',
        ),
      ),
    );
  }

  void _updateDiscoverableByEmail(WidgetRef ref, bool enabled) {
    // TODO: Implement discoverable by email update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'You can now be found by email' 
              : 'You cannot be found by email',
        ),
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: const Text(
          'Password change functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTwoFactorDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Two-Factor Authentication'),
        content: const Text(
          'Two-factor authentication will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDataPrivacyDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data & Privacy'),
        content: const Text(
          'Data and privacy management will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
