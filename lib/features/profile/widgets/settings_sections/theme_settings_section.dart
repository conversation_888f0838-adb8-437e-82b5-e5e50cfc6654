import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/auth/auth.dart';

/// Widget for managing theme and appearance settings
class ThemeSettingsSection extends ConsumerWidget {
  final AppUser user;

  const ThemeSettingsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final preferences = user.preferences;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Appearance',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        Sized<PERSON><PERSON>(height: 12.h),

        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              // Theme Mode Setting
              _buildThemeModeTile(context, ref, preferences.themeMode),

              Divider(
                height: 1,
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),

              // Language Setting
              _buildLanguageTile(context, ref, preferences.languageCode),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildThemeModeTile(
    BuildContext context,
    WidgetRef ref,
    ThemeMode currentMode,
  ) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        _getThemeModeIcon(currentMode),
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        'Theme',
        style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        _getThemeModeLabel(currentMode),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Symbols.chevron_right,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: () => _showThemeModeDialog(context, ref, currentMode),
    );
  }

  Widget _buildLanguageTile(
    BuildContext context,
    WidgetRef ref,
    String currentLanguage,
  ) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        Symbols.language,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        'Language',
        style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        _getLanguageLabel(currentLanguage),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Symbols.chevron_right,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: () => _showLanguageDialog(context, ref, currentLanguage),
    );
  }

  IconData _getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Symbols.light_mode;
      case ThemeMode.dark:
        return Symbols.dark_mode;
      case ThemeMode.system:
        return Symbols.brightness_auto;
    }
  }

  String _getThemeModeLabel(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  String _getLanguageLabel(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'es':
        return 'Spanish';
      case 'fr':
        return 'French';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      default:
        return 'English';
    }
  }

  void _showThemeModeDialog(
    BuildContext context,
    WidgetRef ref,
    ThemeMode currentMode,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return RadioListTile<ThemeMode>(
              value: mode,
              groupValue: currentMode,
              title: Row(
                children: [
                  Icon(_getThemeModeIcon(mode), size: 20.sp),
                  SizedBox(width: 12.w),
                  Text(_getThemeModeLabel(mode)),
                ],
              ),
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  _updateThemeMode(ref, value);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    WidgetRef ref,
    String currentLanguage,
  ) {
    final languages = [
      {'code': 'en', 'name': 'English'},
      {'code': 'es', 'name': 'Spanish'},
      {'code': 'fr', 'name': 'French'},
      {'code': 'de', 'name': 'German'},
      {'code': 'it', 'name': 'Italian'},
      {'code': 'pt', 'name': 'Portuguese'},
      {'code': 'zh', 'name': 'Chinese'},
      {'code': 'ja', 'name': 'Japanese'},
      {'code': 'ko', 'name': 'Korean'},
      {'code': 'ar', 'name': 'Arabic'},
      {'code': 'hi', 'name': 'Hindi'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Language'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: languages.length,
            itemBuilder: (context, index) {
              final language = languages[index];
              return RadioListTile<String>(
                value: language['code']!,
                groupValue: currentLanguage,
                title: Text(language['name']!),
                onChanged: (value) {
                  if (value != null) {
                    Navigator.of(context).pop();
                    _updateLanguage(ref, value);
                  }
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _updateThemeMode(WidgetRef ref, ThemeMode newMode) {
    // TODO: Implement theme mode update with new auth system
    // This will update the user's preferences and persist the change
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text('Theme updated to ${_getThemeModeLabel(newMode)}'),
      ),
    );
  }

  void _updateLanguage(WidgetRef ref, String newLanguage) {
    // TODO: Implement language update with new auth system
    // This will update the user's preferences and persist the change
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text('Language updated to ${_getLanguageLabel(newLanguage)}'),
      ),
    );
  }
}
