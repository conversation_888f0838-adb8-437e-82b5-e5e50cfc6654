import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/auth/auth.dart';

/// Widget for managing notification preferences
class NotificationSettingsSection extends ConsumerWidget {
  final AppUser user;

  const NotificationSettingsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final preferences = user.preferences;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notifications',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        Sized<PERSON><PERSON>(height: 12.h),
        
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              // Push Notifications
              _buildNotificationToggle(
                context,
                ref,
                icon: Symbols.notifications,
                title: 'Push Notifications',
                subtitle: 'Receive notifications on your device',
                value: preferences.pushNotificationsEnabled,
                onChanged: (value) => _updatePushNotifications(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Email Notifications
              _buildNotificationToggle(
                context,
                ref,
                icon: Symbols.email,
                title: 'Email Notifications',
                subtitle: 'Receive notifications via email',
                value: preferences.emailNotificationsEnabled,
                onChanged: (value) => _updateEmailNotifications(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Homework Reminders
              _buildNotificationToggle(
                context,
                ref,
                icon: Symbols.assignment,
                title: 'Homework Reminders',
                subtitle: 'Get reminded about upcoming homework',
                value: preferences.homeworkRemindersEnabled,
                onChanged: (value) => _updateHomeworkReminders(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Announcement Notifications
              _buildNotificationToggle(
                context,
                ref,
                icon: Symbols.campaign,
                title: 'Announcements',
                subtitle: 'Notifications for new announcements',
                value: preferences.announcementNotificationsEnabled,
                onChanged: (value) => _updateAnnouncementNotifications(ref, value),
              ),
              
              _buildDivider(theme),
              
              // Chat Notifications
              _buildNotificationToggle(
                context,
                ref,
                icon: Symbols.chat,
                title: 'Chat Messages',
                subtitle: 'Notifications for new chat messages',
                value: preferences.chatNotificationsEnabled,
                onChanged: (value) => _updateChatNotifications(ref, value),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationToggle(
    BuildContext context,
    WidgetRef ref, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Divider(
      height: 1,
      color: theme.colorScheme.outline.withValues(alpha: 0.2),
    );
  }

  void _updatePushNotifications(WidgetRef ref, bool enabled) {
    // TODO: Implement push notifications update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Push notifications enabled' 
              : 'Push notifications disabled',
        ),
      ),
    );
  }

  void _updateEmailNotifications(WidgetRef ref, bool enabled) {
    // TODO: Implement email notifications update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Email notifications enabled' 
              : 'Email notifications disabled',
        ),
      ),
    );
  }

  void _updateHomeworkReminders(WidgetRef ref, bool enabled) {
    // TODO: Implement homework reminders update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Homework reminders enabled' 
              : 'Homework reminders disabled',
        ),
      ),
    );
  }

  void _updateAnnouncementNotifications(WidgetRef ref, bool enabled) {
    // TODO: Implement announcement notifications update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Announcement notifications enabled' 
              : 'Announcement notifications disabled',
        ),
      ),
    );
  }

  void _updateChatNotifications(WidgetRef ref, bool enabled) {
    // TODO: Implement chat notifications update with new auth system
    ScaffoldMessenger.of(ref.context).showSnackBar(
      SnackBar(
        content: Text(
          enabled 
              ? 'Chat notifications enabled' 
              : 'Chat notifications disabled',
        ),
      ),
    );
  }
}
