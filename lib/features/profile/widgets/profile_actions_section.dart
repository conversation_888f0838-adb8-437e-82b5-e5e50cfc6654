import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/theme/app_colors.dart';

/// Widget displaying profile action buttons and quick stats
class ProfileActionsSection extends StatelessWidget {
  /// The app user to display
  final AppUser user;

  const ProfileActionsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Quick Actions',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 16.h),

        // Action buttons grid
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
            ),
          ),
          child: Column(
            children: [
              // First row of actions
              Row(
                children: [
                  Expanded(
                    child: _ActionButton(
                      icon: Symbols.edit,
                      label: 'Edit Profile',
                      onTap: () => _handleEditProfile(context),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _ActionButton(
                      icon: Symbols.photo_camera,
                      label: 'Change Photo',
                      onTap: () => _handleChangePhoto(context),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Second row of actions
              Row(
                children: [
                  Expanded(
                    child: _ActionButton(
                      icon: Symbols.share,
                      label: 'Share Profile',
                      onTap: () => _handleShareProfile(context),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _ActionButton(
                      icon: Symbols.download,
                      label: 'Export Data',
                      onTap: () => _handleExportData(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // Profile stats
        _buildProfileStats(context),
      ],
    );
  }

  /// Build profile statistics section
  Widget _buildProfileStats(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile Stats',
            style: theme.textTheme.titleSmall?.copyWith(
              color: isDark
                  ? AppColors.textPrimaryDark
                  : AppColors.textPrimaryLight,
              fontWeight: FontWeight.w600,
            ),
          ),

          SizedBox(height: 12.h),

          Row(
            children: [
              Expanded(
                child: _StatItem(
                  icon: Symbols.groups,
                  label: 'Classes',
                  value:
                      '-', // TODO: Query classes where studentIds contains user.id
                ),
              ),
              Expanded(
                child: _StatItem(
                  icon: Symbols.book,
                  label: 'Subjects',
                  value: user.subjects.length.toString(),
                ),
              ),
              Expanded(
                child: _StatItem(
                  icon: Symbols.calendar_today,
                  label: 'Member Since',
                  value: user.createdAt != null
                      ? _getYearFromDate(user.createdAt!)
                      : 'N/A',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get year from date
  String _getYearFromDate(DateTime date) {
    return date.year.toString();
  }

  /// Handle edit profile action
  void _handleEditProfile(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text(
          'Profile editing functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Handle change photo action
  void _handleChangePhoto(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Photo'),
        content: const Text(
          'Photo upload functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Handle share profile action
  void _handleShareProfile(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Profile'),
        content: const Text(
          'Profile sharing functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Handle export data action
  void _handleExportData(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text(
          'Data export functionality will be implemented in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Widget for action buttons
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: (isDark ? AppColors.borderDark : AppColors.borderLight)
                .withValues(alpha: 0.5),
          ),
        ),
        child: Column(
          children: [
            Icon(icon, size: 24.sp, color: theme.colorScheme.primary),
            SizedBox(height: 4.h),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget for stat items
class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: [
        Icon(icon, size: 20.sp, color: theme.colorScheme.primary),
        SizedBox(height: 4.h),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.labelSmall?.copyWith(
            color: isDark
                ? AppColors.textSecondaryDark
                : AppColors.textSecondaryLight,
          ),
        ),
      ],
    );
  }
}
