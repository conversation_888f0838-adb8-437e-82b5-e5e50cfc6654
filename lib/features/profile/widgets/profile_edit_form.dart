import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/enums/auth_enums.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/forms/custom_text_field.dart';

/// Form widget for editing user profile information
class ProfileEditForm extends StatefulWidget {
  final AppUser user;
  final VoidCallback onChanged;
  final Function(AppUser) onSave;

  const ProfileEditForm({
    super.key,
    required this.user,
    required this.onChanged,
    required this.onSave,
  });

  @override
  State<ProfileEditForm> createState() => _ProfileEditFormState();
}

class _ProfileEditFormState extends State<ProfileEditForm> {
  late final TextEditingController _fullNameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _gradeController;
  late final TextEditingController _schoolController;
  late final TextEditingController _studentIdController;
  late final TextEditingController _bioController;
  late final TextEditingController _addressController;
  late final TextEditingController _emergencyContactController;
  late final TextEditingController _emergencyPhoneController;
  late final TextEditingController _parentNameController;
  late final TextEditingController _parentPhoneController;
  late final TextEditingController _parentEmailController;

  late UserType _selectedUserType;
  DateTime? _selectedDateOfBirth;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _fullNameController = TextEditingController(
      text: widget.user.fullName ?? '',
    );
    _phoneController = TextEditingController(
      text: widget.user.phoneNumber ?? '',
    );
    _gradeController = TextEditingController(text: widget.user.grade ?? '');
    _schoolController = TextEditingController(text: widget.user.school ?? '');
    _studentIdController = TextEditingController(
      text: widget.user.studentId ?? '',
    );
    _bioController = TextEditingController(text: widget.user.bio ?? '');
    _addressController = TextEditingController(text: widget.user.address ?? '');
    _emergencyContactController = TextEditingController(
      text: widget.user.emergencyContact ?? '',
    );
    _emergencyPhoneController = TextEditingController(
      text: widget.user.emergencyContactPhone ?? '',
    );
    _parentNameController = TextEditingController(
      text: widget.user.parentGuardianName ?? '',
    );
    _parentPhoneController = TextEditingController(
      text: widget.user.parentGuardianPhone ?? '',
    );
    _parentEmailController = TextEditingController(
      text: widget.user.parentGuardianEmail ?? '',
    );

    _selectedUserType = widget.user.userType;
    _selectedDateOfBirth = widget.user.dateOfBirth;

    // Add listeners to detect changes
    _fullNameController.addListener(widget.onChanged);
    _phoneController.addListener(widget.onChanged);
    _gradeController.addListener(widget.onChanged);
    _schoolController.addListener(widget.onChanged);
    _studentIdController.addListener(widget.onChanged);
    _bioController.addListener(widget.onChanged);
    _addressController.addListener(widget.onChanged);
    _emergencyContactController.addListener(widget.onChanged);
    _emergencyPhoneController.addListener(widget.onChanged);
    _parentNameController.addListener(widget.onChanged);
    _parentPhoneController.addListener(widget.onChanged);
    _parentEmailController.addListener(widget.onChanged);
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _gradeController.dispose();
    _schoolController.dispose();
    _studentIdController.dispose();
    _bioController.dispose();
    _addressController.dispose();
    _emergencyContactController.dispose();
    _emergencyPhoneController.dispose();
    _parentNameController.dispose();
    _parentPhoneController.dispose();
    _parentEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile Picture Section
        _buildProfilePictureSection(context, isDark),

        SizedBox(height: 24.h),

        // Basic Information Section
        _buildSectionTitle(context, 'Basic Information'),
        SizedBox(height: 16.h),
        _buildBasicInfoSection(context),

        SizedBox(height: 24.h),

        // Academic Information Section
        _buildSectionTitle(context, 'Academic Information'),
        SizedBox(height: 16.h),
        _buildAcademicInfoSection(context),

        SizedBox(height: 24.h),

        // Personal Information Section
        _buildSectionTitle(context, 'Personal Information'),
        SizedBox(height: 16.h),
        _buildPersonalInfoSection(context),

        SizedBox(height: 24.h),

        // Emergency Contact Section
        _buildSectionTitle(context, 'Emergency Contact'),
        SizedBox(height: 16.h),
        _buildEmergencyContactSection(context),

        if (_selectedUserType == UserType.student) ...[
          SizedBox(height: 24.h),

          // Parent/Guardian Information Section
          _buildSectionTitle(context, 'Parent/Guardian Information'),
          SizedBox(height: 16.h),
          _buildParentInfoSection(context),
        ],

        SizedBox(height: 32.h),

        // Save Button
        _buildSaveButton(context),

        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildProfilePictureSection(BuildContext context, bool isDark) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        children: [
          // Profile avatar
          Stack(
            children: [
              Container(
                width: 100.w,
                height: 100.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: ClipOval(
                  child:
                      (widget.user.profileImageUrl != null &&
                              widget.user.profileImageUrl!.isNotEmpty) ||
                          (widget.user.photoUrl != null &&
                              widget.user.photoUrl!.isNotEmpty)
                      ? Image.network(
                          widget.user.profileImageUrl ?? widget.user.photoUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultAvatar(context);
                          },
                        )
                      : _buildDefaultAvatar(context),
                ),
              ),

              // Edit button
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 32.w,
                  height: 32.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isDark
                          ? AppColors.surfaceDark
                          : AppColors.surfaceLight,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Symbols.camera_alt,
                    size: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          TextButton.icon(
            onPressed: () {
              // TODO: Implement image picker
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Image picker will be implemented'),
                ),
              );
            },
            icon: const Icon(Symbols.photo_library),
            label: const Text('Change Photo'),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Symbols.person,
        size: 40.sp,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildBasicInfoSection(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          controller: _fullNameController,
          labelText: 'Full Name',
          hintText: 'Enter your full name',
          prefixIcon: Icon(Symbols.person),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Full name is required';
            }
            return null;
          },
        ),

        SizedBox(height: 16.h),

        // Email field (read-only)
        TextFormField(
          initialValue: widget.user.email,
          enabled: false,
          decoration: InputDecoration(
            labelText: 'Email',
            hintText: 'Email address',
            prefixIcon: Icon(Symbols.email),
            helperText: 'Email cannot be changed',
            filled: true,
            fillColor: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.04),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 16.h,
            ),
          ),
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _phoneController,
          labelText: 'Phone Number',
          hintText: 'Enter your phone number',
          prefixIcon: Icon(Symbols.phone),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              // Basic phone validation
              if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
                return 'Please enter a valid phone number';
              }
            }
            return null;
          },
        ),

        SizedBox(height: 16.h),

        // User Type Dropdown
        DropdownButtonFormField<UserType>(
          value: _selectedUserType,
          decoration: InputDecoration(
            labelText: 'User Type',
            prefixIcon: Icon(Symbols.badge),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 16.h,
            ),
          ),
          items: UserType.values
              .map(
                (type) => DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                ),
              )
              .toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedUserType = value;
              });
              widget.onChanged();
            }
          },
        ),
      ],
    );
  }

  Widget _buildAcademicInfoSection(BuildContext context) {
    return Column(
      children: [
        if (_selectedUserType == UserType.student) ...[
          CustomTextField(
            controller: _gradeController,
            labelText: 'Grade',
            hintText: 'Enter your grade (e.g., 10, 11, 12)',
            prefixIcon: Icon(Symbols.school),
          ),

          SizedBox(height: 16.h),

          CustomTextField(
            controller: _studentIdController,
            labelText: 'Student ID',
            hintText: 'Enter your student ID',
            prefixIcon: Icon(Symbols.badge),
          ),

          SizedBox(height: 16.h),
        ],

        CustomTextField(
          controller: _schoolController,
          labelText: 'School',
          hintText: 'Enter your school name',
          prefixIcon: Icon(Symbols.domain),
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _bioController,
          labelText: 'Bio',
          hintText: 'Tell us about yourself',
          prefixIcon: Icon(Symbols.description),
          maxLines: 3,
          maxLength: 500,
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection(BuildContext context) {
    return Column(
      children: [
        // Date of Birth
        InkWell(
          onTap: () => _selectDateOfBirth(context),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.5),
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Icon(
                  Symbols.cake,
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date of Birth',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        _selectedDateOfBirth != null
                            ? _formatDate(_selectedDateOfBirth!)
                            : 'Select date of birth',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: _selectedDateOfBirth != null
                              ? Theme.of(context).colorScheme.onSurface
                              : Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Symbols.calendar_today,
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _addressController,
          labelText: 'Address',
          hintText: 'Enter your address',
          prefixIcon: Icon(Symbols.location_on),
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildEmergencyContactSection(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          controller: _emergencyContactController,
          labelText: 'Emergency Contact Name',
          hintText: 'Enter emergency contact name',
          prefixIcon: Icon(Symbols.emergency),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Emergency contact is required';
            }
            return null;
          },
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _emergencyPhoneController,
          labelText: 'Emergency Contact Phone',
          hintText: 'Enter emergency contact phone',
          prefixIcon: Icon(Symbols.phone),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Emergency contact phone is required';
            }
            if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
              return 'Please enter a valid phone number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildParentInfoSection(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          controller: _parentNameController,
          labelText: 'Parent/Guardian Name',
          hintText: 'Enter parent or guardian name',
          prefixIcon: Icon(Symbols.family_restroom),
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _parentPhoneController,
          labelText: 'Parent/Guardian Phone',
          hintText: 'Enter parent or guardian phone',
          prefixIcon: Icon(Symbols.phone),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
                return 'Please enter a valid phone number';
              }
            }
            return null;
          },
        ),

        SizedBox(height: 16.h),

        CustomTextField(
          controller: _parentEmailController,
          labelText: 'Parent/Guardian Email',
          hintText: 'Enter parent or guardian email',
          prefixIcon: Icon(Symbols.email),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Please enter a valid email address';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSaveButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _handleSave(),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: Text(
          'Save Changes',
          style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  void _selectDateOfBirth(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDateOfBirth ??
          DateTime.now().subtract(const Duration(days: 365 * 16)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
      widget.onChanged();
    }
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  void _handleSave() {
    final updatedUser = widget.user.copyWith(
      fullName: _fullNameController.text.trim().isNotEmpty
          ? _fullNameController.text.trim()
          : null,
      phoneNumber: _phoneController.text.trim().isNotEmpty
          ? _phoneController.text.trim()
          : null,
      userType: _selectedUserType,
      grade: _gradeController.text.trim().isNotEmpty
          ? _gradeController.text.trim()
          : null,
      school: _schoolController.text.trim().isNotEmpty
          ? _schoolController.text.trim()
          : null,
      studentId: _studentIdController.text.trim().isNotEmpty
          ? _studentIdController.text.trim()
          : null,
      bio: _bioController.text.trim().isNotEmpty
          ? _bioController.text.trim()
          : null,
      dateOfBirth: _selectedDateOfBirth,
      address: _addressController.text.trim().isNotEmpty
          ? _addressController.text.trim()
          : null,
      emergencyContact: _emergencyContactController.text.trim().isNotEmpty
          ? _emergencyContactController.text.trim()
          : null,
      emergencyContactPhone: _emergencyPhoneController.text.trim().isNotEmpty
          ? _emergencyPhoneController.text.trim()
          : null,
      parentGuardianName: _parentNameController.text.trim().isNotEmpty
          ? _parentNameController.text.trim()
          : null,
      parentGuardianPhone: _parentPhoneController.text.trim().isNotEmpty
          ? _parentPhoneController.text.trim()
          : null,
      parentGuardianEmail: _parentEmailController.text.trim().isNotEmpty
          ? _parentEmailController.text.trim()
          : null,
    );

    widget.onSave(updatedUser);
  }
}
