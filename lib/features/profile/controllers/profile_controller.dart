import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/auth/auth.dart';
import '../../../core/enums/auth_enums.dart';
import '../models/profile_model.dart';
import '../models/profile_update_model.dart';
import '../repositories/profile_repository.dart';
import '../services/mock_profile_service.dart';

/// Logger for profile controller
final _logger = Logger();

/// Provider for the profile repository instance
final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  return ProfileRepository();
});

/// Provider to get current user from new auth system
final currentAppUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authStateProvider);

  if (authState.status == AuthStatus.authenticated) {
    return authState.user;
  }

  return null;
});

/// Provider to check if current user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.status == AuthStatus.authenticated;
});

/// Provider for mock profile service (for testing)
final mockProfileServiceProvider = Provider<MockProfileService>((ref) {
  return MockProfileService();
});

/// Provider to get mock current user for testing
final mockCurrentUserProvider = Provider<AppUser?>((ref) {
  return MockProfileService.getCurrentMockUser();
});

/// Provider to get all mock users for testing
final mockAllUsersProvider = Provider<List<AppUser>>((ref) {
  return MockProfileService.getAllMockUsers();
});

/// Provider to get mock users by type
final mockUsersByTypeProvider = Provider.family<List<AppUser>, UserType>((
  ref,
  userType,
) {
  return MockProfileService.getMockUsersByType(userType);
});

/// Provider to fetch current user's profile
final currentUserProfileProvider = FutureProvider<ProfileModel?>((ref) async {
  final authState = ref.watch(authStateProvider);

  // Check if user is authenticated
  if (authState.status != AuthStatus.authenticated || authState.user == null) {
    _logger.w('No authenticated user found');
    return null;
  }

  final repository = ref.read(profileRepositoryProvider);

  try {
    _logger.i('Fetching profile for current user: ${authState.user!.email}');
    final profile = await repository.getProfileById(authState.user!.id);

    if (profile != null) {
      _logger.i('Successfully fetched current user profile');
    } else {
      _logger.w('No profile found for current user');
    }

    return profile;
  } catch (e) {
    _logger.e('Error fetching current user profile: $e');
    rethrow;
  }
});

/// Provider to fetch a profile by user ID
final profileByIdProvider = FutureProvider.family<ProfileModel?, String>((
  ref,
  userId,
) async {
  final repository = ref.read(profileRepositoryProvider);

  try {
    _logger.i('Fetching profile for user ID: $userId');
    final profile = await repository.getProfileById(userId);

    if (profile != null) {
      _logger.i('Successfully fetched profile for user: ${profile.fullName}');
    } else {
      _logger.w('No profile found for user ID: $userId');
    }

    return profile;
  } catch (e) {
    _logger.e('Error fetching profile by ID: $e');
    rethrow;
  }
});

/// Provider to fetch profiles by class ID
final profilesByClassProvider =
    FutureProvider.family<List<ProfileModel>, String>((ref, classId) async {
      final repository = ref.read(profileRepositoryProvider);

      try {
        _logger.i('Fetching profiles for class ID: $classId');
        final profiles = await repository.getProfilesByClassId(classId);
        _logger.i('Successfully fetched ${profiles.length} profiles for class');
        return profiles;
      } catch (e) {
        _logger.e('Error fetching profiles by class: $e');
        rethrow;
      }
    });

/// Provider to fetch profiles by user type
final profilesByUserTypeProvider =
    FutureProvider.family<List<ProfileModel>, UserType>((ref, userType) async {
      final repository = ref.read(profileRepositoryProvider);

      try {
        _logger.i('Fetching profiles for user type: ${userType.displayName}');
        final profiles = await repository.getProfilesByUserType(userType.value);
        _logger.i(
          'Successfully fetched ${profiles.length} profiles for user type',
        );
        return profiles;
      } catch (e) {
        _logger.e('Error fetching profiles by user type: $e');
        rethrow;
      }
    });

/// Provider to search profiles by name
final searchProfilesProvider =
    FutureProvider.family<List<ProfileModel>, String>((ref, searchTerm) async {
      if (searchTerm.trim().isEmpty) {
        return [];
      }

      final repository = ref.read(profileRepositoryProvider);

      try {
        _logger.i('Searching profiles with term: $searchTerm');
        final profiles = await repository.searchProfilesByName(searchTerm);
        _logger.i(
          'Successfully found ${profiles.length} profiles matching search',
        );
        return profiles;
      } catch (e) {
        _logger.e('Error searching profiles: $e');
        rethrow;
      }
    });

/// Provider to check if a profile exists for a user
final profileExistsProvider = FutureProvider.family<bool, String>((
  ref,
  userId,
) async {
  final repository = ref.read(profileRepositoryProvider);

  try {
    _logger.i('Checking if profile exists for user ID: $userId');
    final exists = await repository.profileExists(userId);
    _logger.i('Profile exists for user ID $userId: $exists');
    return exists;
  } catch (e) {
    _logger.e('Error checking profile existence: $e');
    rethrow;
  }
});

/// State notifier for profile mutations
class ProfileMutationNotifier extends StateNotifier<AsyncValue<void>> {
  ProfileMutationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Create a new profile
  Future<ProfileModel> createProfile(ProfileModel profile) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(profileRepositoryProvider);

      _logger.i('Creating profile for user: ${profile.fullName}');
      final createdProfile = await repository.createProfile(profile);

      // Invalidate related providers
      _ref.invalidate(currentUserProfileProvider);
      _ref.invalidate(profileByIdProvider);
      _ref.invalidate(profilesByClassProvider);
      _ref.invalidate(profilesByUserTypeProvider);
      _ref.invalidate(profileExistsProvider);

      _logger.i('Successfully created profile');
      return createdProfile;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (profile) => profile,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, _) => throw error,
      );
    });
  }

  /// Update an existing profile
  Future<ProfileModel> updateProfile(
    String userId,
    ProfileUpdateModel updateModel,
  ) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(profileRepositoryProvider);

      _logger.i('Updating profile for user ID: $userId');
      final updatedProfile = await repository.updateProfile(
        userId,
        updateModel,
      );

      // Invalidate related providers
      _ref.invalidate(currentUserProfileProvider);
      _ref.invalidate(profileByIdProvider);
      _ref.invalidate(profilesByClassProvider);
      _ref.invalidate(profilesByUserTypeProvider);
      _ref.invalidate(searchProfilesProvider);

      _logger.i('Successfully updated profile');
      return updatedProfile;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (profile) => profile,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, _) => throw error,
      );
    });
  }

  /// Delete a profile
  Future<void> deleteProfile(String userId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(profileRepositoryProvider);

      _logger.i('Deleting profile for user ID: $userId');
      await repository.deleteProfile(userId);

      // Invalidate related providers
      _ref.invalidate(currentUserProfileProvider);
      _ref.invalidate(profileByIdProvider);
      _ref.invalidate(profilesByClassProvider);
      _ref.invalidate(profilesByUserTypeProvider);
      _ref.invalidate(searchProfilesProvider);
      _ref.invalidate(profileExistsProvider);

      _logger.i('Successfully deleted profile');
    });
  }
}

/// Provider for profile mutations
final profileMutationProvider =
    StateNotifierProvider<ProfileMutationNotifier, AsyncValue<void>>((ref) {
      return ProfileMutationNotifier(ref);
    });

/// Helper provider to get current user's profile or create one if it doesn't exist
final currentUserProfileOrCreateProvider = FutureProvider<ProfileModel?>((
  ref,
) async {
  final authState = ref.watch(authStateProvider);

  // Check if user is authenticated
  if (authState.status != AuthStatus.authenticated || authState.user == null) {
    _logger.w('No authenticated user found');
    return null;
  }

  final user = authState.user!;
  final repository = ref.read(profileRepositoryProvider);

  try {
    // First, try to get existing profile
    final existingProfile = await repository.getProfileById(user.id);
    if (existingProfile != null) {
      return existingProfile;
    }

    // If no profile exists, create a basic one from user data
    _logger.i('Creating basic profile for new user: ${user.email}');
    final newProfile = ProfileModel(
      id: user.id,
      fullName: user.fullName ?? user.displayName ?? 'User',
      email: user.email,
      userType: user.userType,
      profileImageUrl: user.profileImageUrl,
      grade: user.grade,
      school: user.school,
      studentId: user.studentId,
      bio: user.bio,
      subjects: user.subjects,
      phoneNumber: user.phoneNumber,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createProfile(newProfile);
  } catch (e) {
    _logger.e('Error getting or creating user profile: $e');
    rethrow;
  }
});
