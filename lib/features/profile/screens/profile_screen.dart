import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../controllers/profile_controller.dart' hide isAuthenticatedProvider;
import '../widgets/profile_info_section.dart';
import '../widgets/profile_header.dart';
import '../widgets/profile_actions_section.dart';

/// Screen displaying user profile information
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentAppUserProvider);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Profile',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
          ),
          backgroundColor: theme.colorScheme.surface,
          elevation: 0,
          actions: [
            if (currentUser != null) ...[
              IconButton(
                icon: const Icon(Symbols.edit),
                onPressed: () {
                  // TODO: Navigate to edit profile screen
                  _showEditProfileDialog(context, ref);
                },
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'settings':
                      // TODO: Navigate to settings
                      break;
                    case 'help':
                      // TODO: Navigate to help
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Symbols.settings),
                        SizedBox(width: 8),
                        Text('Settings'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'help',
                    child: Row(
                      children: [
                        Icon(Symbols.help),
                        SizedBox(width: 8),
                        Text('Help'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        body: _buildBody(context, ref, currentUser, isAuthenticated),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    WidgetRef ref,
    AppUser? currentUser,
    bool isAuthenticated,
  ) {
    if (!isAuthenticated || currentUser == null) {
      return _buildNoProfileState(context, ref);
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Refresh auth state to get latest user data
        ref.invalidate(authStateProvider);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header with avatar and basic info
            ProfileHeader(user: currentUser),

            SizedBox(height: 24.h),

            // Profile information section
            ProfileInfoSection(user: currentUser),

            SizedBox(height: 24.h),

            // Profile actions section
            ProfileActionsSection(user: currentUser),

            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  /// Build no profile state (when user is not authenticated)
  Widget _buildNoProfileState(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Symbols.login, size: 48.sp, color: theme.colorScheme.primary),
            SizedBox(height: 16.h),
            Text(
              'Not Signed In',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Please sign in to view your profile',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                // Navigate to login screen
                _navigateToLogin(context);
              },
              child: const Text('Sign In'),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to login screen
  void _navigateToLogin(BuildContext context) {
    // TODO: Implement navigation to login screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigation to login screen will be implemented'),
      ),
    );
  }

  /// Navigate to edit profile screen
  void _showEditProfileDialog(BuildContext context, WidgetRef ref) {
    context.pushNamed(RouteNames.editProfile);
  }
}
