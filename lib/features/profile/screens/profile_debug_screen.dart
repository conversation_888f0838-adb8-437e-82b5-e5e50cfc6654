import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/enums/auth_enums.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../controllers/profile_controller.dart';
import '../services/mock_profile_service.dart';

/// Debug screen for testing profile system with mock data
class ProfileDebugScreen extends ConsumerWidget {
  const ProfileDebugScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final mockCurrentUser = ref.watch(mockCurrentUserProvider);
    final mockAllUsers = ref.watch(mockAllUsersProvider);

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Profile Debug',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
          ),
          backgroundColor: theme.colorScheme.surface,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Symbols.arrow_back),
            onPressed: () => context.pop(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Symbols.refresh),
              onPressed: () {
                // Refresh providers
                ref.invalidate(mockCurrentUserProvider);
                ref.invalidate(mockAllUsersProvider);
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Mock Data Statistics
              _buildStatisticsSection(context, theme),

              SizedBox(height: 24.h),

              // Current Mock User
              _buildCurrentUserSection(context, theme, mockCurrentUser),

              SizedBox(height: 24.h),

              // All Mock Users
              _buildAllUsersSection(context, theme, mockAllUsers),

              SizedBox(height: 24.h),

              // Actions
              _buildActionsSection(context, theme, ref),

              SizedBox(height: 24.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(BuildContext context, ThemeData theme) {
    final stats = MockProfileService.getMockDataStatistics();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mock Data Statistics',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 12.h),

        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatItem('Total Users', stats['totalUsers'].toString()),
              SizedBox(height: 8.h),
              _buildStatItem(
                'Current User ID',
                stats['currentUserId'] ?? 'None',
              ),
              SizedBox(height: 8.h),
              _buildStatItem(
                'Has Valid Current User',
                stats['hasValidCurrentUser'].toString(),
              ),

              SizedBox(height: 12.h),

              Text(
                'User Type Breakdown:',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              SizedBox(height: 8.h),

              ...((stats['userTypeBreakdown'] as Map<String, dynamic>).entries
                  .map(
                    (entry) => Padding(
                      padding: EdgeInsets.only(left: 16.w, bottom: 4.h),
                      child: _buildStatItem(entry.key, entry.value.toString()),
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
      ],
    );
  }

  Widget _buildCurrentUserSection(
    BuildContext context,
    ThemeData theme,
    AppUser? currentUser,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Mock User',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 12.h),

        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: currentUser != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUserInfoRow('Name', currentUser.fullName ?? 'N/A'),
                    _buildUserInfoRow('Email', currentUser.email),
                    _buildUserInfoRow(
                      'User Type',
                      currentUser.userType.displayName,
                    ),
                    _buildUserInfoRow('Status', currentUser.status.toString()),
                    _buildUserInfoRow('Grade', currentUser.grade ?? 'N/A'),
                    _buildUserInfoRow('School', currentUser.school ?? 'N/A'),
                    _buildUserInfoRow(
                      'Subjects',
                      currentUser.subjects.join(', '),
                    ),
                  ],
                )
              : Text(
                  'No current user found',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildUserInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildAllUsersSection(
    BuildContext context,
    ThemeData theme,
    List<AppUser> allUsers,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'All Mock Users (${allUsers.length})',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 12.h),

        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: allUsers.length > 10
                ? 10
                : allUsers.length, // Show first 10
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
            itemBuilder: (context, index) {
              final user = allUsers[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.primary.withValues(
                    alpha: 0.1,
                  ),
                  child: Icon(Symbols.person, color: theme.colorScheme.primary),
                ),
                title: Text(user.fullName ?? user.displayName ?? 'Unknown'),
                subtitle: Text('${user.userType.displayName} • ${user.email}'),
                trailing: user.id == MockProfileService.getCurrentMockUser()?.id
                    ? Icon(Symbols.star, color: theme.colorScheme.primary)
                    : null,
              );
            },
          ),
        ),

        if (allUsers.length > 10) ...[
          SizedBox(height: 8.h),
          Text(
            'Showing first 10 of ${allUsers.length} users',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionsSection(
    BuildContext context,
    ThemeData theme,
    WidgetRef ref,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 12.h),

        Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _validateMockData(context),
                icon: const Icon(Symbols.check_circle),
                label: const Text('Validate Mock Data Integration'),
              ),
            ),

            SizedBox(height: 12.h),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testProfileScreen(context),
                icon: const Icon(Symbols.person),
                label: const Text('Test Profile Screen'),
              ),
            ),

            SizedBox(height: 12.h),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testSettingsScreen(context),
                icon: const Icon(Symbols.settings),
                label: const Text('Test Settings Screen'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _validateMockData(BuildContext context) {
    final isValid = MockProfileService.validateMockDataIntegration();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isValid
              ? 'Mock data integration validation passed!'
              : 'Mock data integration validation failed!',
        ),
        backgroundColor: isValid
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.error,
      ),
    );
  }

  void _testProfileScreen(BuildContext context) {
    context.pushNamed(RouteNames.profile);
  }

  void _testSettingsScreen(BuildContext context) {
    context.pushNamed(RouteNames.settings);
  }
}
