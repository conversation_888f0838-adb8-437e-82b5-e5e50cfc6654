import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/auth/auth.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../controllers/profile_controller.dart' hide isAuthenticatedProvider;
import '../widgets/profile_edit_form.dart';

/// Screen for editing user profile information
class ProfileEditScreen extends ConsumerStatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  ConsumerState<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends ConsumerState<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _hasUnsavedChanges = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentAppUserProvider);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return ResponsivePage(
      mobile: (context) => PopScope(
        canPop: !_hasUnsavedChanges,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop && _hasUnsavedChanges) {
            _showUnsavedChangesDialog(context);
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              'Edit Profile',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
            backgroundColor: theme.colorScheme.surface,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Symbols.arrow_back),
              onPressed: () => _handleBackPress(context),
            ),
            actions: [
              TextButton(
                onPressed: _hasUnsavedChanges
                    ? () => _saveChanges(context)
                    : null,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: _hasUnsavedChanges
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          body: _buildBody(context, currentUser, isAuthenticated),
        ),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    AppUser? currentUser,
    bool isAuthenticated,
  ) {
    if (!isAuthenticated || currentUser == null) {
      return _buildNotAuthenticatedState(context);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: ProfileEditForm(
          user: currentUser,
          onChanged: () {
            if (!_hasUnsavedChanges) {
              setState(() {
                _hasUnsavedChanges = true;
              });
            }
          },
          onSave: (updatedUser) => _handleSave(context, updatedUser),
        ),
      ),
    );
  }

  Widget _buildNotAuthenticatedState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Symbols.login, size: 48.sp, color: theme.colorScheme.primary),
            SizedBox(height: 16.h),
            Text(
              'Not Signed In',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Please sign in to edit your profile',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => context.pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  void _handleBackPress(BuildContext context) {
    if (_hasUnsavedChanges) {
      _showUnsavedChangesDialog(context);
    } else {
      context.pop();
    }
  }

  void _showUnsavedChangesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text(
          'You have unsaved changes. Are you sure you want to leave without saving?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.pop();
            },
            child: const Text('Leave'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _saveChanges(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _saveChanges(BuildContext context) {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();
    }
  }

  void _handleSave(BuildContext context, AppUser updatedUser) async {
    try {
      // TODO: Implement profile update logic with new auth system
      // This will be implemented when we create the profile update service

      setState(() {
        _hasUnsavedChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Profile updated successfully!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
