import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../core/auth/auth.dart';
import '../../../core/enums/auth_enums.dart' hide AuthState;
import '../../debug/mock_data/mock_data.dart';
import '../models/profile_model.dart';

/// Service for converting mock ProfileModel data to AppUser for testing
class MockProfileService {
  static final _logger = Logger();

  /// Convert ProfileModel to AppUser for new auth system integration
  static AppUser convertProfileToAppUser(ProfileModel profile) {
    return AppUser(
      // Firebase Auth fields
      id: profile.id,
      email: profile.email,
      displayName: profile.fullName,
      photoUrl: profile.profileImageUrl,
      emailVerified: true, // Mock data assumes verified emails
      createdAt: profile.createdAt,
      lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
      phoneNumber: profile.phoneNumber,

      // Profile fields
      fullName: profile.fullName,
      userType: profile.userType,
      status: UserStatus.active, // Mock users are active
      primaryClassId: profile.primaryClassId,
      profileImageUrl: profile.profileImageUrl,
      grade: profile.grade,
      school: profile.school,
      studentId: profile.studentId,
      bio: profile.bio,
      subjects: profile.subjects,
      dateOfBirth: profile.dateOfBirth,
      address: profile.address,
      emergencyContact: profile.emergencyContact,
      emergencyContactPhone: profile.emergencyContactPhone,
      parentGuardianName: profile.parentGuardianName,
      parentGuardianPhone: profile.parentGuardianPhone,
      parentGuardianEmail: profile.parentGuardianEmail,

      // Chat fields (defaults for mock data)
      chatPermission: _getChatPermissionForUserType(profile.userType),
      isChatAvailable: true,
      approvedChatUsers: [], // Empty for mock data
      // App fields with mock preferences
      preferences: _createMockPreferences(),
    );
  }

  /// Get all mock users as AppUser objects
  static List<AppUser> getAllMockUsers() {
    try {
      return mockProfilesList.map(convertProfileToAppUser).toList();
    } catch (e) {
      _logger.e('Error converting mock profiles to AppUsers: $e');
      return [];
    }
  }

  /// Get current mock user as AppUser
  static AppUser? getCurrentMockUser() {
    try {
      const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
      final profile = mockProfilesList.firstWhere(
        (p) => p.id == currentUserId,
        orElse: () => throw Exception('Current user not found'),
      );
      return convertProfileToAppUser(profile);
    } catch (e) {
      _logger.e('Error getting current mock user: $e');
      return null;
    }
  }

  /// Get mock users by type
  static List<AppUser> getMockUsersByType(UserType userType) {
    try {
      return mockProfilesList
          .where((p) => p.userType == userType)
          .map(convertProfileToAppUser)
          .toList();
    } catch (e) {
      _logger.e('Error getting mock users by type: $e');
      return [];
    }
  }

  /// Get mock user by ID
  static AppUser? getMockUserById(String id) {
    try {
      final profile = mockProfilesList.firstWhere(
        (p) => p.id == id,
        orElse: () => throw Exception('User not found'),
      );
      return convertProfileToAppUser(profile);
    } catch (e) {
      _logger.e('Error getting mock user by ID: $e');
      return null;
    }
  }

  /// Create mock user preferences
  static UserPreferences _createMockPreferences() {
    return const UserPreferences(
      // Theme settings
      themeMode: ThemeMode.system,
      languageCode: 'en',

      // Notification settings
      pushNotificationsEnabled: true,
      emailNotificationsEnabled: true,
      homeworkRemindersEnabled: true,
      announcementNotificationsEnabled: true,
      chatNotificationsEnabled: true,

      // Privacy settings
      profileVisibilityEnabled: true,
      discoverableByEmail: true,

      // App settings
      defaultUploadQuality: 'medium',
      autoDownloadOnWifi: true,
      autoDownloadOnMobile: false,
    );
  }

  /// Get appropriate chat permission based on user type
  static ChatPermission _getChatPermissionForUserType(UserType userType) {
    switch (userType) {
      case UserType.student:
        return ChatPermission.canOnlyRespond;
      case UserType.parent:
        return ChatPermission.canOnlyRespond;
      case UserType.teacher:
        return ChatPermission.canInitiateAndRespond;
      case UserType.admin:
        return ChatPermission.canInitiateAndRespond;
      case UserType.other:
        return ChatPermission.canOnlyRespond;
    }
  }

  /// Validate mock data integration
  static bool validateMockDataIntegration() {
    try {
      _logger.i('Validating mock data integration with new auth system...');

      // Test current user conversion
      final currentUser = getCurrentMockUser();
      if (currentUser == null) {
        _logger.e('Failed to get current mock user');
        return false;
      }

      // Test all users conversion
      final allUsers = getAllMockUsers();
      if (allUsers.isEmpty) {
        _logger.e('Failed to convert any mock users');
        return false;
      }

      // Test user type filtering
      final students = getMockUsersByType(UserType.student);
      final teachers = getMockUsersByType(UserType.teacher);
      final admins = getMockUsersByType(UserType.admin);

      _logger.i('Mock data integration validation results:');
      _logger.i(
        '- Current user: ${currentUser.fullName} (${currentUser.email})',
      );
      _logger.i('- Total users: ${allUsers.length}');
      _logger.i('- Students: ${students.length}');
      _logger.i('- Teachers: ${teachers.length}');
      _logger.i('- Admins: ${admins.length}');

      // Validate data integrity
      for (final user in allUsers) {
        if (user.id.isEmpty || user.email.isEmpty) {
          _logger.e('Invalid user data found: ${user.id}');
          return false;
        }
      }

      _logger.i('Mock data integration validation passed successfully');
      return true;
    } catch (e) {
      _logger.e('Error validating mock data integration: $e');
      return false;
    }
  }

  /// Create a mock auth state for testing
  static AuthState createMockAuthState({AppUser? user}) {
    final mockUser = user ?? getCurrentMockUser();

    if (mockUser == null) {
      return AuthState(
        status: AuthStatus.unauthenticated,
        user: null,
        isLoading: false,
        error: null,
        lastUpdated: DateTime.now(),
      );
    }

    return AuthState(
      status: AuthStatus.authenticated,
      user: mockUser,
      isLoading: false,
      error: null,
      lastUpdated: DateTime.now(),
    );
  }

  /// Get mock statistics for debugging
  static Map<String, dynamic> getMockDataStatistics() {
    final allUsers = getAllMockUsers();
    final userTypeStats = <UserType, int>{};

    for (final user in allUsers) {
      userTypeStats[user.userType] = (userTypeStats[user.userType] ?? 0) + 1;
    }

    return {
      'totalUsers': allUsers.length,
      'userTypeBreakdown': userTypeStats.map(
        (k, v) => MapEntry(k.toString(), v),
      ),
      'currentUserId': getCurrentMockUser()?.id,
      'hasValidCurrentUser': getCurrentMockUser() != null,
    };
  }
}
