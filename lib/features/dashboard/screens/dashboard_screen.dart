import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:scholara_student/core/providers/auth_providers.dart';
import 'package:scholara_student/core/routes/app_routes.dart';
import 'package:scholara_student/core/widgets/responsive/responsive_page.dart';
import 'package:scholara_student/core/widgets/theme/theme_mode_switcher.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Dashboard',
            style: textTheme.titleLarge?.copyWith(color: colorScheme.onSurface),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          automaticallyImplyLeading: false, // Remove back button
          actions: [
            const ThemeModeSwitcher(),
            PopupMenuButton<String>(
              onSelected: (value) async {
                if (value == 'logout') {
                  await ref.read(authStateProvider.notifier).signOut();
                  if (context.mounted) {
                    context.goNamed(RouteNames.login);
                  }
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Symbols.logout),
                      SizedBox(width: 8),
                      Text('Logout'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            DashboardTile(
              title: 'Homework',
              icon: Symbols.assignment,
              onTap: () {
                context.pushNamed(RouteNames.homeworkList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Classrooms',
              icon: Symbols.school,
              onTap: () {
                context.pushNamed(RouteNames.classroomsList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Study Plans',
              icon: Symbols.timeline,
              onTap: () {
                context.pushNamed(RouteNames.studyPlansList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Profile',
              icon: Symbols.person,
              onTap: () {
                context.pushNamed(RouteNames.profile);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Digital Library',
              icon: Symbols.folder,
              onTap: () {
                context.pushNamed(RouteNames.digitalLibrary);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Chat',
              icon: Symbols.chat,
              onTap: () {
                context.pushNamed(RouteNames.chatList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Announcements',
              icon: Symbols.campaign,
              onTap: () {
                context.pushNamed(RouteNames.announcementsList);
              },
            ),

            // Debug tiles (only show in debug mode)
            if (kDebugMode) ...[
              const SizedBox(height: 24),
              Text(
                'Debug Tools',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Debug Info',
                icon: Symbols.info,
                onTap: () {
                  context.pushNamed(RouteNames.debugInfo);
                },
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Debug Logs',
                icon: Symbols.list_alt,
                onTap: () {
                  context.pushNamed(RouteNames.debugLogs);
                },
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Mock Data Management',
                icon: Symbols.data_object,
                onTap: () {
                  context.pushNamed(RouteNames.mockDataManagement);
                },
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Profile Debug',
                icon: Symbols.person_search,
                onTap: () {
                  context.pushNamed(RouteNames.profileDebug);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class DashboardTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  const DashboardTile({
    super.key,
    required this.title,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(icon, size: 28, color: colorScheme.primary),
            const SizedBox(width: 16),
            Text(
              title,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Icon(
              Symbols.arrow_forward_ios_rounded,
              size: 16,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }
}
