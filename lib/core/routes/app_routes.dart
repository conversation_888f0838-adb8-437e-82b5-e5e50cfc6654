class AppRoutes {
  // Authentication routes
  static const String login = '/login';
  static const String signUp = '/signup';
  static const String forgotPassword = '/forgot-password';

  // Main app routes
  static const String home = '/';
  static const String homeworkList = '/homework';
  static const String homeworkDetail = '/homework/:id';
  static const String submitHomework = '/homework/:id/submit';
  static const String viewSubmission = '/homework/:id/submission';

  // Classroom routes
  static const String classroomsList = '/classrooms';
  static const String classroomDetail = '/classrooms/:id';
  static const String activityFeed = '/classrooms/:id/activity';
  static const String classroomResources = '/classrooms/:id/resources';
  static const String classroomDiscussion = '/classrooms/:id/discussion';

  // Profile routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String settings = '/profile/settings';
  static const String profileDebug = '/profile/debug';

  // Digital Library routes
  static const String digitalLibrary = '/library';
  static const String librarySearch = '/library/search';
  static const String libraryAllItems = '/library/all';
  static const String folderDetail = '/library/folder/:id';
  static const String fileDetail = '/library/file/:id';
  static const String uploadFile = '/library/upload';

  // Announcements routes
  static const String announcementsList = '/announcements';
  static const String announcementDetail = '/announcements/:id';
  static const String announcementsCategories = '/announcements/categories';
  static const String announcementsSearch = '/announcements/search';

  // Study Plan routes
  static const String studyPlansList = '/study-plans';
  static const String studyPlanDetail = '/study-plans/:id';
  static const String studyPlanTemplates = '/study-plans/templates';
  static const String studyPlanProgress = '/study-plans/:id/progress';
  static const String studyPlanStructure = '/study-plans/:id/structure';
  static const String studyPlanCreate = '/study-plans/create';
  static const String studyPlanEdit = '/study-plans/:id/edit';

  // Chat routes
  static const String chatList = '/chat';
  static const String chatDetail = '/chat/:id';
  static const String chatParticipants = '/chat/:id/participants';
  static const String chatSettings = '/chat/:id/settings';
  static const String chatSearch = '/chat/search';
  static const String createChat = '/chat/create';

  // Debug routes
  static const String debugInfo = '/debug/info';
  static const String debugLogs = '/debug/logs';
  static const String mockDataManagement = '/debug/mock-data';
}

class RouteNames {
  // Authentication route names
  static const String login = 'login';
  static const String signUp = 'signUp';
  static const String forgotPassword = 'forgotPassword';

  // Main app route names
  static const String home = 'home';
  static const String homeworkList = 'homeworkList';
  static const String homeworkDetail = 'homeworkDetail';
  static const String submitHomework = 'submitHomework';
  static const String viewSubmission = 'viewSubmission';

  // Classroom route names
  static const String classroomsList = 'classroomsList';
  static const String classroomDetail = 'classroomDetail';
  static const String activityFeed = 'activityFeed';
  static const String classroomResources = 'classroomResources';
  static const String classroomDiscussion = 'classroomDiscussion';

  // Profile route names
  static const String profile = 'profile';
  static const String editProfile = 'editProfile';
  static const String settings = 'settings';
  static const String profileDebug = 'profileDebug';

  // Digital Library route names
  static const String digitalLibrary = 'digitalLibrary';
  static const String librarySearch = 'librarySearch';
  static const String libraryAllItems = 'libraryAllItems';
  static const String folderDetail = 'folderDetail';
  static const String fileDetail = 'fileDetail';
  static const String uploadFile = 'uploadFile';

  // Announcements route names
  static const String announcementsList = 'announcementsList';
  static const String announcementDetail = 'announcementDetail';
  static const String announcementsCategories = 'announcementsCategories';
  static const String announcementsSearch = 'announcementsSearch';

  // Study Plan route names
  static const String studyPlansList = 'studyPlansList';
  static const String studyPlanDetail = 'studyPlanDetail';
  static const String studyPlanTemplates = 'studyPlanTemplates';
  static const String studyPlanProgress = 'studyPlanProgress';
  static const String studyPlanStructure = 'studyPlanStructure';
  static const String studyPlanCreate = 'studyPlanCreate';
  static const String studyPlanEdit = 'studyPlanEdit';

  // Chat route names
  static const String chatList = 'chatList';
  static const String chatDetail = 'chatDetail';
  static const String chatParticipants = 'chatParticipants';
  static const String chatSettings = 'chatSettings';
  static const String chatSearch = 'chatSearch';
  static const String createChat = 'createChat';

  // Debug route names
  static const String debugInfo = 'debugInfo';
  static const String debugLogs = 'debugLogs';
  static const String mockDataManagement = 'mockDataManagement';
}
