import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:scholara_student/features/auth/screens/login_screen.dart';
import 'package:scholara_student/features/auth/screens/sign_up_screen.dart';
import 'package:scholara_student/features/dashboard/screens/dashboard_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_list/homework_list_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_detail/homework_detail_screen.dart';
import 'package:scholara_student/features/homework/screens/submit_homework/submit_homework_screen.dart';
import 'package:scholara_student/features/homework/screens/view_submission/view_submission_screen.dart';
import 'package:scholara_student/features/profile/screens/profile_screen.dart';
import 'package:scholara_student/features/profile/screens/profile_edit_screen.dart';
import 'package:scholara_student/features/profile/screens/settings_screen.dart';
import 'package:scholara_student/features/profile/screens/profile_debug_screen.dart';
import 'package:scholara_student/features/debug/screens/debug_info_screen.dart';
import 'package:scholara_student/features/debug/screens/debug_logs_screen.dart';
import 'package:scholara_student/features/debug/screens/mock_data_screen.dart';
import 'package:scholara_student/features/digital_library/screens/main_library_screen/main_library_screen.dart';
import 'package:scholara_student/features/digital_library/screens/search_screen/search_screen.dart';
import 'package:scholara_student/features/digital_library/screens/all_items_screen/all_items_screen.dart';
import 'package:scholara_student/features/digital_library/screens/folder_detail_screen/folder_detail_screen.dart';
import 'package:scholara_student/features/announcements/screens/announcements_list/announcements_list_screen.dart';
import 'package:scholara_student/features/announcements/screens/announcement_detail/announcement_detail_screen.dart';
import 'package:scholara_student/features/announcements/screens/categories/categories_screen.dart';
import 'package:scholara_student/features/announcements/screens/search/search_screen.dart'
    as announcements_search;
import 'package:scholara_student/features/digital_library/screens/file_detail_screen/file_detail_screen.dart';
import 'package:scholara_student/features/digital_library/screens/upload_file_screen/upload_file_screen.dart';
import 'package:scholara_student/features/classroom/screens/classrooms_list/classrooms_list_screen.dart';
import 'package:scholara_student/features/classroom/screens/classroom_detail/classroom_detail_screen.dart';
import 'package:scholara_student/features/classroom/screens/activity_feed/activity_feed_screen.dart';
import 'package:scholara_student/features/classroom/screens/resources/resources_screen.dart';
import 'package:scholara_student/features/classroom/screens/discussion/discussion_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plans_list/study_plans_list_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plan_detail/study_plan_detail_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plan_templates/study_plan_templates_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plan_progress/study_plan_progress_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plan_structure/study_plan_structure_screen.dart';
import 'package:scholara_student/features/study_plan/screens/study_plan_create_edit/study_plan_create_edit_screen.dart';
import 'package:scholara_student/features/chat/screens/chat_list/chat_list_screen.dart';
import 'package:scholara_student/features/chat/screens/chat_detail/chat_detail_screen.dart';
import 'package:scholara_student/features/chat/screens/chat_participants/chat_participants_screen.dart';
import 'package:scholara_student/features/chat/screens/chat_settings/chat_settings_screen.dart';
import 'package:scholara_student/features/chat/screens/create_chat/create_chat_screen.dart';
import 'package:scholara_student/features/chat/enums/chat_type.dart';
import '../auth/auth.dart';
import 'app_routes.dart';

// Router provider with authentication redirect logic
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.home,
    redirect: (context, state) {
      final authState = ref.read(authStateProvider);
      final authStatus = authState.status;

      // If auth is still loading, don't redirect yet
      if (authStatus.isLoading) {
        return null;
      }

      final isAuthRoute =
          state.matchedLocation == AppRoutes.login ||
          state.matchedLocation == AppRoutes.signUp ||
          state.matchedLocation == AppRoutes.forgotPassword;

      final isAuthenticated = authStatus.isAuthenticated;
      final canAccessApp = authStatus.canAccessApp;

      // Handle special auth states that require user action
      if (authStatus == AuthStatus.requiresEmailVerification && !isAuthRoute) {
        // TODO: Add email verification route when implemented
        return AppRoutes.login; // For now, redirect to login
      }

      if (authStatus == AuthStatus.requiresProfileSetup && !isAuthRoute) {
        // TODO: Add profile setup route when implemented
        return AppRoutes.login; // For now, redirect to login
      }

      // If user is authenticated and can access app, but trying to access auth routes, redirect to home
      if (canAccessApp && isAuthRoute) {
        return AppRoutes.home;
      }

      // Only redirect unauthenticated users trying to access protected routes
      if (!isAuthenticated && !isAuthRoute) {
        return AppRoutes.login;
      }

      // No automatic redirect for authenticated users - let them navigate freely
      return null;
    },
    routes: [
      // Authentication routes
      GoRoute(
        path: AppRoutes.login,
        name: RouteNames.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.signUp,
        name: RouteNames.signUp,
        builder: (context, state) => const SignUpScreen(),
      ),

      // Main app routes
      GoRoute(
        path: AppRoutes.home,
        name: RouteNames.home,
        builder: (context, state) => const DashboardScreen(),
      ),
      GoRoute(
        path: AppRoutes.homeworkList,
        name: RouteNames.homeworkList,
        builder: (context, state) => const HomeworkListScreen(),
      ),
      GoRoute(
        path: AppRoutes.homeworkDetail,
        name: RouteNames.homeworkDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return HomeworkDetailScreen(homeworkId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.submitHomework,
        name: RouteNames.submitHomework,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return SubmitHomeworkScreen(homeworkId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.viewSubmission,
        name: RouteNames.viewSubmission,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ViewSubmissionScreen(homeworkId: id);
        },
      ),

      // Classroom routes
      GoRoute(
        path: AppRoutes.classroomsList,
        name: RouteNames.classroomsList,
        builder: (context, state) => const ClassroomsListScreen(),
      ),
      GoRoute(
        path: AppRoutes.classroomDetail,
        name: RouteNames.classroomDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ClassroomDetailScreen(classroomId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.activityFeed,
        name: RouteNames.activityFeed,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ActivityFeedScreen(classroomId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.classroomResources,
        name: RouteNames.classroomResources,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ResourcesScreen(classroomId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.classroomDiscussion,
        name: RouteNames.classroomDiscussion,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return DiscussionScreen(classroomId: id);
        },
      ),

      // Profile routes
      GoRoute(
        path: AppRoutes.profile,
        name: RouteNames.profile,
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: AppRoutes.editProfile,
        name: RouteNames.editProfile,
        builder: (context, state) => const ProfileEditScreen(),
      ),
      GoRoute(
        path: AppRoutes.settings,
        name: RouteNames.settings,
        builder: (context, state) => const SettingsScreen(),
      ),

      // Digital Library routes
      GoRoute(
        path: AppRoutes.digitalLibrary,
        name: RouteNames.digitalLibrary,
        builder: (context, state) => const MainLibraryScreen(),
      ),
      GoRoute(
        path: AppRoutes.librarySearch,
        name: RouteNames.librarySearch,
        builder: (context, state) => const LibrarySearchScreen(),
      ),
      GoRoute(
        path: AppRoutes.libraryAllItems,
        name: RouteNames.libraryAllItems,
        builder: (context, state) {
          final filter = state.uri.queryParameters['filter'];
          final usage = state.uri.queryParameters['usage'];
          return AllItemsScreen(initialFilter: filter, initialUsage: usage);
        },
      ),
      GoRoute(
        path: AppRoutes.folderDetail,
        name: RouteNames.folderDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return FolderDetailScreen(folderId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.fileDetail,
        name: RouteNames.fileDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return FileDetailScreen(fileId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.uploadFile,
        name: RouteNames.uploadFile,
        builder: (context, state) => const UploadFileScreen(),
      ),

      // Announcements routes
      GoRoute(
        path: AppRoutes.announcementsList,
        name: RouteNames.announcementsList,
        builder: (context, state) {
          final filter = state.uri.queryParameters['filter'];
          final classroomId = state.uri.queryParameters['classroomId'];
          return AnnouncementsListScreen(
            initialFilter: filter,
            classroomId: classroomId,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.announcementDetail,
        name: RouteNames.announcementDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return AnnouncementDetailScreen(announcementId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.announcementsCategories,
        name: RouteNames.announcementsCategories,
        builder: (context, state) => const CategoriesScreen(),
      ),
      GoRoute(
        path: AppRoutes.announcementsSearch,
        name: RouteNames.announcementsSearch,
        builder: (context, state) {
          final query = state.uri.queryParameters['q'];
          return announcements_search.SearchScreen(initialQuery: query);
        },
      ),

      // Study Plan routes
      GoRoute(
        path: AppRoutes.studyPlansList,
        name: RouteNames.studyPlansList,
        builder: (context, state) {
          final classroomId = state.uri.queryParameters['classroomId'];
          return StudyPlansListScreen(classroomId: classroomId);
        },
      ),
      // More specific routes first (before parameterized routes)
      GoRoute(
        path: AppRoutes.studyPlanTemplates,
        name: RouteNames.studyPlanTemplates,
        builder: (context, state) => const StudyPlanTemplatesScreen(),
      ),
      GoRoute(
        path: AppRoutes.studyPlanCreate,
        name: RouteNames.studyPlanCreate,
        builder: (context, state) {
          final templateId = state.uri.queryParameters['templateId'];
          return StudyPlanCreateEditScreen(templateId: templateId);
        },
      ),
      // Parameterized routes after specific routes
      GoRoute(
        path: AppRoutes.studyPlanDetail,
        name: RouteNames.studyPlanDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return StudyPlanDetailScreen(studyPlanId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.studyPlanProgress,
        name: RouteNames.studyPlanProgress,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return StudyPlanProgressScreen(studyPlanId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.studyPlanStructure,
        name: RouteNames.studyPlanStructure,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return StudyPlanStructureScreen(studyPlanId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.studyPlanEdit,
        name: RouteNames.studyPlanEdit,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return StudyPlanCreateEditScreen(studyPlanId: id);
        },
      ),

      // Chat routes
      GoRoute(
        path: AppRoutes.chatList,
        name: RouteNames.chatList,
        builder: (context, state) => const ChatListScreen(),
      ),
      // Put specific routes before parameterized routes to avoid conflicts
      GoRoute(
        path: AppRoutes.createChat,
        name: RouteNames.createChat,
        builder: (context, state) {
          final chatTypeParam = state.uri.queryParameters['type'];
          final stepParam = state.uri.queryParameters['step'];
          final modeParam = state.uri.queryParameters['mode'];

          ChatType? initialChatType;
          if (chatTypeParam != null) {
            try {
              initialChatType = ChatType.values.firstWhere(
                (type) => type.name == chatTypeParam,
              );
            } catch (e) {
              // Invalid chat type, ignore
            }
          }

          return CreateChatScreen(
            initialChatType: initialChatType,
            initialStep: stepParam,
            selectionMode: modeParam,
          );
        },
      ),
      // Parameterized routes come after specific routes
      GoRoute(
        path: AppRoutes.chatDetail,
        name: RouteNames.chatDetail,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ChatDetailScreen(chatId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.chatParticipants,
        name: RouteNames.chatParticipants,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ChatParticipantsScreen(chatId: id);
        },
      ),
      GoRoute(
        path: AppRoutes.chatSettings,
        name: RouteNames.chatSettings,
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return ChatSettingsScreen(chatId: id);
        },
      ),

      // Debug routes
      GoRoute(
        path: AppRoutes.debugInfo,
        name: RouteNames.debugInfo,
        builder: (context, state) => const DebugInfoScreen(),
      ),
      GoRoute(
        path: AppRoutes.debugLogs,
        name: RouteNames.debugLogs,
        builder: (context, state) => const DebugLogsScreen(),
      ),
      GoRoute(
        path: AppRoutes.mockDataManagement,
        name: RouteNames.mockDataManagement,
        builder: (context, state) => const MockDataManagementScreen(),
      ),
      GoRoute(
        path: AppRoutes.profileDebug,
        name: RouteNames.profileDebug,
        builder: (context, state) => const ProfileDebugScreen(),
      ),
    ],
  );
});

// Legacy router for backward compatibility (without redirect logic)
final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login,
  routes: [
    // Authentication routes
    GoRoute(
      path: AppRoutes.login,
      name: RouteNames.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: AppRoutes.signUp,
      name: RouteNames.signUp,
      builder: (context, state) => const SignUpScreen(),
    ),

    // Main app routes
    GoRoute(
      path: AppRoutes.home,
      name: RouteNames.home,
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: AppRoutes.homeworkList,
      name: RouteNames.homeworkList,
      builder: (context, state) {
        final classroomId = state.uri.queryParameters['classroomId'];
        return HomeworkListScreen(classroomId: classroomId);
      },
    ),
    GoRoute(
      path: AppRoutes.homeworkDetail,
      name: RouteNames.homeworkDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return HomeworkDetailScreen(homeworkId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.submitHomework,
      name: RouteNames.submitHomework,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return SubmitHomeworkScreen(homeworkId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.viewSubmission,
      name: RouteNames.viewSubmission,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ViewSubmissionScreen(homeworkId: id);
      },
    ),

    // Classroom routes
    GoRoute(
      path: AppRoutes.classroomsList,
      name: RouteNames.classroomsList,
      builder: (context, state) => const ClassroomsListScreen(),
    ),
    GoRoute(
      path: AppRoutes.classroomDetail,
      name: RouteNames.classroomDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ClassroomDetailScreen(classroomId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.activityFeed,
      name: RouteNames.activityFeed,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ActivityFeedScreen(classroomId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.classroomResources,
      name: RouteNames.classroomResources,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ResourcesScreen(classroomId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.classroomDiscussion,
      name: RouteNames.classroomDiscussion,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return DiscussionScreen(classroomId: id);
      },
    ),

    // Digital Library routes
    GoRoute(
      path: AppRoutes.digitalLibrary,
      name: RouteNames.digitalLibrary,
      builder: (context, state) => const MainLibraryScreen(),
    ),
    GoRoute(
      path: AppRoutes.librarySearch,
      name: RouteNames.librarySearch,
      builder: (context, state) => const LibrarySearchScreen(),
    ),
    GoRoute(
      path: AppRoutes.libraryAllItems,
      name: RouteNames.libraryAllItems,
      builder: (context, state) {
        final filter = state.uri.queryParameters['filter'];
        final usage = state.uri.queryParameters['usage'];
        return AllItemsScreen(initialFilter: filter, initialUsage: usage);
      },
    ),
    GoRoute(
      path: AppRoutes.folderDetail,
      name: RouteNames.folderDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return FolderDetailScreen(folderId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.fileDetail,
      name: RouteNames.fileDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return FileDetailScreen(fileId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.uploadFile,
      name: RouteNames.uploadFile,
      builder: (context, state) => const UploadFileScreen(),
    ),

    // Announcements routes
    GoRoute(
      path: AppRoutes.announcementsList,
      name: RouteNames.announcementsList,
      builder: (context, state) {
        final filter = state.uri.queryParameters['filter'];
        final classroomId = state.uri.queryParameters['classroomId'];
        return AnnouncementsListScreen(
          initialFilter: filter,
          classroomId: classroomId,
        );
      },
    ),
    GoRoute(
      path: AppRoutes.announcementDetail,
      name: RouteNames.announcementDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return AnnouncementDetailScreen(announcementId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.announcementsCategories,
      name: RouteNames.announcementsCategories,
      builder: (context, state) => const CategoriesScreen(),
    ),
    GoRoute(
      path: AppRoutes.announcementsSearch,
      name: RouteNames.announcementsSearch,
      builder: (context, state) {
        final query = state.uri.queryParameters['q'];
        return announcements_search.SearchScreen(initialQuery: query);
      },
    ),

    // Study Plan routes
    GoRoute(
      path: AppRoutes.studyPlansList,
      name: RouteNames.studyPlansList,
      builder: (context, state) {
        final classroomId = state.uri.queryParameters['classroomId'];
        return StudyPlansListScreen(classroomId: classroomId);
      },
    ),
    // More specific routes first (before parameterized routes)
    GoRoute(
      path: AppRoutes.studyPlanTemplates,
      name: RouteNames.studyPlanTemplates,
      builder: (context, state) => const StudyPlanTemplatesScreen(),
    ),
    GoRoute(
      path: AppRoutes.studyPlanCreate,
      name: RouteNames.studyPlanCreate,
      builder: (context, state) {
        final templateId = state.uri.queryParameters['templateId'];
        return StudyPlanCreateEditScreen(templateId: templateId);
      },
    ),
    // Parameterized routes after specific routes
    GoRoute(
      path: AppRoutes.studyPlanDetail,
      name: RouteNames.studyPlanDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return StudyPlanDetailScreen(studyPlanId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.studyPlanProgress,
      name: RouteNames.studyPlanProgress,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return StudyPlanProgressScreen(studyPlanId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.studyPlanStructure,
      name: RouteNames.studyPlanStructure,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return StudyPlanStructureScreen(studyPlanId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.studyPlanEdit,
      name: RouteNames.studyPlanEdit,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return StudyPlanCreateEditScreen(studyPlanId: id);
      },
    ),

    // Chat routes
    GoRoute(
      path: AppRoutes.chatList,
      name: RouteNames.chatList,
      builder: (context, state) => const ChatListScreen(),
    ),
    GoRoute(
      path: AppRoutes.chatDetail,
      name: RouteNames.chatDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ChatDetailScreen(chatId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.chatParticipants,
      name: RouteNames.chatParticipants,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ChatParticipantsScreen(chatId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.chatSettings,
      name: RouteNames.chatSettings,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return ChatSettingsScreen(chatId: id);
      },
    ),

    // Debug routes
    GoRoute(
      path: AppRoutes.debugInfo,
      name: RouteNames.debugInfo,
      builder: (context, state) => const DebugInfoScreen(),
    ),
    GoRoute(
      path: AppRoutes.debugLogs,
      name: RouteNames.debugLogs,
      builder: (context, state) => const DebugLogsScreen(),
    ),
    GoRoute(
      path: AppRoutes.mockDataManagement,
      name: RouteNames.mockDataManagement,
      builder: (context, state) => const MockDataManagementScreen(),
    ),
  ],
);

// context.pushNamed() -->	Navigate forward (adds to stack)
// context.goNamed() -->	Navigate and clear previous stack
// pathParameters	--> For dynamic routes like /homework/:id
// initialLocation -->	Define default route on app start
// GoRouterRefreshStream -->	Auto-redirect when auth state changes (advanced)
